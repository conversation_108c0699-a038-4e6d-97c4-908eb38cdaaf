package fileio

import (
	"encoding/binary"
	"fmt"
	"io"

	"os"
)

type FileIo struct {
	FileName string
	Ctype    string
}

type Record struct {
	LineNumber uint32
	VLen       uint32
	Value      string
}

func (f *FileIo) DumpMapToFileGetInx(data []Record) error {
	// Create or truncate the file
	file_path := "/Users/<USER>/workspace/indexing_poc/" + f.Ctype

	file, err := os.Create(file_path + "/" + f.FileName)
	if err != nil {
		return err
	}
	defer file.Close()

	nRecords := len(data)
	var nRecordsBuffer, valueBuffer []byte
	indexBuffer := make([]byte, 0, nRecords*12)
	nRecordsBuffer = make([]byte, 4)

	binary.BigEndian.PutUint32(nRecordsBuffer, uint32(nRecords))
	file.Write(nRecordsBuffer)
	valueOffsetStart := 4 + (nRecords * 12)
	for _, val := range data {
		var lineNumberBuffer, byteOffset, vLenBuffer []byte
		lineNumberBuffer = make([]byte, 4)
		byteOffset = make([]byte, 4)
		vLenBuffer = make([]byte, 4)

		binary.BigEndian.PutUint32(lineNumberBuffer, uint32(val.LineNumber))
		binary.BigEndian.PutUint32(byteOffset, uint32(valueOffsetStart))
		binary.BigEndian.PutUint32(vLenBuffer, uint32(val.VLen))
		valueOffsetStart = valueOffsetStart + int(val.VLen)

		indexBuffer = append(indexBuffer, lineNumberBuffer...)
		// file.Write(lineNumberBuffer)
		// file.Write(byteOffset)
		// file.Write(vLenBuffer)
		indexBuffer = append(indexBuffer, byteOffset...)
		indexBuffer = append(indexBuffer, vLenBuffer...)

		valueBuffer = append(valueBuffer, []byte(val.Value)...)
	}
	file.Write(indexBuffer)
	file.Write(valueBuffer)
	file.Close()

	return nil
}

type RecordInxValue struct {
	ListStartOffset uint32
	valueOffset     uint32
}

func (f *FileIo) DumpMapToFileSearchEx(data map[string][]int, invertedInxData []Record) error {
	// Create or truncate the file
	file_path := "/Users/<USER>/workspace/indexing_poc/" + f.Ctype
	file, err := os.Create(file_path + "/" + f.FileName)

	if err != nil {
		return err
	}
	defer file.Close()
	fmt.Println(file_path + "/" + f.FileName)
	// Place holder for inverted index data
	uniqValueslist, uniqueValueTotalBytes := getKeyLenRecord(data)
	// Get total number of bytes required put unique values data
	// in three Blocks
	// <<value Len (4 bytes)>><<Start offset of line number list (4 Bytes)>><<Actul value string>>
	// map hold offset of each individule value
	uniqValueOffsetMap, offset := getValuesOffset(file, uniqValueslist)

	TotalBytesWrittenBuff := make([]byte, int64(offset))
	// Create place holder for Uniq field value and assosiated offset for line number list
	file.Write(TotalBytesWrittenBuff)
	// Dump list of line number field value and line number list
	TotalBytesWritten := dumpIndexData(file, data, uniqValueOffsetMap, offset)

	// Dump unique values and offset of line number list
	file.Seek(8, io.SeekStart)
	dumpUniqueValues(file, uniqValueOffsetMap)
	file.Seek(0, io.SeekStart)
	lastOffset := 8 + TotalBytesWritten + uint32(uniqueValueTotalBytes)
	binary.BigEndian.PutUint32(TotalBytesWrittenBuff[0:4], uint32(lastOffset))
	binary.BigEndian.PutUint32(TotalBytesWrittenBuff[4:8], uint32(uniqueValueTotalBytes+8))
	file.Write(TotalBytesWrittenBuff[0:8])
	file.Seek(int64(lastOffset), io.SeekStart)
	dumpInvertedIndexData(file, invertedInxData, lastOffset, uniqValueOffsetMap)
	return nil

}

func dumpUniqueValues(file *os.File, uniqValueOffsetMap map[string]RecordInxValue) {
	offset := uint32(8)
	for key, val := range uniqValueOffsetMap {
		offsetBuffer := make([]byte, 4)
		valueLenBuffer := make([]byte, 4)
		binary.BigEndian.PutUint32(offsetBuffer, val.ListStartOffset)
		binary.BigEndian.PutUint32(valueLenBuffer, uint32(len(key)))
		file.Write(valueLenBuffer)
		file.Write(offsetBuffer)
		file.Write([]byte(key))
		tempInexValue := uniqValueOffsetMap[key]
		tempInexValue.valueOffset = offset
		uniqValueOffsetMap[key] = tempInexValue
		offset = offset + uint32(8) + uint32(len(key))
	}
}

func getValuesOffset(file *os.File, uniqValueslist []Record) (map[string]RecordInxValue, uint32) {
	uniqValueOffsetMap := make(map[string]RecordInxValue)
	// First two bytes will be the total index data length unique values length
	offset := uint32(8)
	for _, val := range uniqValueslist {
		var recordInxValue RecordInxValue
		recordInxValue.valueOffset = uint32(offset)
		uniqValueOffsetMap[val.Value] = recordInxValue
		offset = offset + uint32(8) + uint32(val.VLen)
	}
	return uniqValueOffsetMap, offset

}

func getKeyLenRecord(data map[string][]int) ([]Record, int) {

	var uniqValueslist []Record
	totalLen := 0

	for key, _ := range data {
		totalLen = totalLen + len(key) + 8 // 4 bytes for key length
		uniqValueslist = append(uniqValueslist, Record{LineNumber: 0, VLen: uint32(len(key)), Value: key})

	}
	return uniqValueslist, totalLen
}

func dumpInvertedIndexData(file *os.File, invertedInxData []Record, lastOffset uint32, uniqValueMap map[string]RecordInxValue) {
	nRecords := len(invertedInxData)
	var nRecordsBuffer []byte
	indexBuffer := make([]byte, 0, nRecords*8)
	nRecordsBuffer = make([]byte, 4)
	//var valueBlockList []RecordInx

	binary.BigEndian.PutUint32(nRecordsBuffer, uint32(nRecords))
	file.Write(nRecordsBuffer)
	//valueOffset := uint32(4 + (nRecords * 8) + int(lastOffset))
	//valueOffset := uint32(0)
	for _, val := range invertedInxData {
		var lineNumberBuffer, byteOffset []byte
		lineNumberBuffer = make([]byte, 4)
		byteOffset = make([]byte, 4)
		// To keep only unique values to reduce file size keep a map of values
		// and only offset will be written to inverted index block
		// if _, ok := mapData[val.Value]; !ok {
		// 	mapData[val.Value] = valueOffset
		// 	binary.BigEndian.PutUint32(vLenBuffer, uint32(val.VLen))
		// 	valueBuffer = append(valueBuffer, vLenBuffer...)
		// 	valueBuffer = append(valueBuffer, []byte(val.Value)...)
		// 	valueOffset = valueOffset + uint32(4) + val.VLen

		// }
		temp := uniqValueMap[val.Value]
		binary.BigEndian.PutUint32(lineNumberBuffer, uint32(val.LineNumber))
		binary.BigEndian.PutUint32(byteOffset, uint32(temp.valueOffset))

		indexBuffer = append(indexBuffer, lineNumberBuffer...)
		// file.Write(lineNumberBuffer)
		// file.Write(byteOffset)
		// file.Write(vLenBuffer)
		indexBuffer = append(indexBuffer, byteOffset...)

	}

	file.Write(indexBuffer)
	file.Close()

}

func dumpIndexData(file *os.File, data map[string][]int, uniqValueMap map[string]RecordInxValue, offset uint32) uint32 {
	var TotalBytesWritten uint32

	offsetListLenbytes := make([]byte, 4)
	offsetListbytes := make([]byte, 4)
	startOffset := offset
	for key, value := range data {
		// keyLen := len(key)
		valueLen := len(value)
		tempValueList := uniqValueMap[key]
		tempValueList.ListStartOffset = startOffset
		uniqValueMap[key] = tempValueList
		// binary.BigEndian.PutUint32(keyLenbytes, uint32(keyLen))
		binary.BigEndian.PutUint32(offsetListLenbytes, uint32(valueLen))
		file.Write(offsetListLenbytes)

		for _, val := range value {

			binary.BigEndian.PutUint32(offsetListbytes, uint32(val))
			file.Write(offsetListbytes)

		}
		startOffset = startOffset + uint32(4+valueLen*4)
		TotalBytesWritten = TotalBytesWritten + uint32(4+valueLen*4)

	}

	return TotalBytesWritten
}
