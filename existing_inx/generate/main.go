package main

import (
	"encoding/binary"
	"encoding/csv"
	"fmt"
	"os"
	"strings"
	fileio "ultrafast_standalone/existing_inx/file_io"
)

type files struct {
	FileName string
	FileFd   *os.File
}

func main() {
	// Create a map
	columnsList := []string{"timestamp", "source_ip", "destination_ip",
		"source_port", "destination_port", "protocol",
		"action", "rule_name", "rule_id",
		"rule_category", "rule_description", "source_country",
		"destination_country", "source_username", "destination_username",
		"source_mac_address", "destination_mac_address", "source_hostname",
		"destination_hostname", "source_os",
		"destination_os", "source_device_type", "destination_device_type",
		"source_location", "destination_location", "source_latitude",
		"source_longitude", "destination_latitude", "destination_longitude",
		"bytes_sent", "bytes_received", "message"}
	GetMap := map[string]map[int32]string{}
	SearchMap := map[string]map[string][]int{}
	fuzzyMap := map[string]map[string][]uint32{}
	existingMap := map[string]files{}
	GetMapInx := map[string][]fileio.Record{}

	for _, val := range columnsList {
		GetMap[val] = map[int32]string{}

	}

	for _, val := range columnsList {
		SearchMap[val] = map[string][]int{}
	}
	for _, val := range columnsList {
		fuzzyMap[val] = map[string][]uint32{}
	}
	for _, val := range columnsList {
		var efile files
		var err error
		efile.FileName = val + "_ex.txt"
		efile.FileFd, err = os.OpenFile("/Users/<USER>/workspace/indexing_poc/existing/"+efile.FileName, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0644)
		if err != nil {
			fmt.Println(err)
			return
		}
		existingMap[val] = efile
	}

	file, err := os.Open("/Users/<USER>/workspace/indexing_poc/mock_data.csv")
	if err != nil {
		fmt.Println("Error opening file:", err)
		return
	}
	defer file.Close()

	// Create a new CSV reader
	reader := csv.NewReader(file)

	// Read all records from CSV
	records, err := reader.ReadAll()
	if err != nil {
		fmt.Println("Error reading CSV:", err)
		return
	}
	flag := 0

	// Print each record
	offsetByteSlice := make([]byte, 4)
	sizeByteSlice := make([]byte, 4)
	for line, row := range records {
		for i, col := range row {

			if col != "" {
				tempMap := map[string]bool{}
				words := strings.Fields(col)

				// Print each word
				for _, word := range words {
					if _, ok := fuzzyMap[word]; !ok {
						fuzzyMap[columnsList[i]][word] = append(fuzzyMap[columnsList[i]][word], uint32(line))
						tempMap[word] = true
					} else if _, ok = tempMap[word]; !ok {
						fuzzyMap[columnsList[i]][word] = append(fuzzyMap[columnsList[i]][word], uint32(line))
					}
				}

				//existingMap[columnsList[i]].FileFd.WriteString(strconv.Itoa(rec) + "^" + col + "\n")
				binary.BigEndian.PutUint32(offsetByteSlice, uint32(line))
				binary.BigEndian.PutUint32(sizeByteSlice, uint32(len(col)))
				existingMap[columnsList[i]].FileFd.Write(offsetByteSlice)
				existingMap[columnsList[i]].FileFd.Write(sizeByteSlice)
				existingMap[columnsList[i]].FileFd.Write([]byte(col))

				GetMap[columnsList[i]][int32(line)] = col
				var rec fileio.Record
				rec.LineNumber = uint32(line)
				rec.Value = col

				rec.VLen = uint32(len(col))

				GetMapInx[columnsList[i]] = append(GetMapInx[columnsList[i]], rec)

				if _, ok := SearchMap[columnsList[i]][col]; ok {
					SearchMap[columnsList[i]][col] = append(SearchMap[columnsList[i]][col], line)
					continue
				}
				if col != "" {
					SearchMap[columnsList[i]][col] = []int{line}

				}
			}
			if columnsList[i] == "rule_name" && flag == 0 && len(fuzzyMap[columnsList[i]]["bala"]) > 3 {
				fmt.Println(fuzzyMap[columnsList[i]]["bala"])
				flag = 1
			}

		}

	}

	for col, val := range SearchMap {
		var f fileio.FileIo
		f.FileName = col + "_inx_com.txt"
		f.Ctype = "search"

		err = f.DumpMapToFileSearchEx(val, GetMapInx[col])
		if err != nil {
			fmt.Println("Error dumping map to file:", err)
			return
		}

	}

}
